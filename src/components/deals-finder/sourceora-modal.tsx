"use client";

import React, { useEffect } from "react";
import { ExternalLink, Loader2, Search } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useTheme } from "@/hooks/useTheme";
import { SourceoraFilter } from "./inspectora-result";
import { useSourceora } from "@/hooks/use-post-source";

interface SourceOraModalProps {
  isOpen: boolean;
  onClose: () => void;
  sourceoraFilter: SourceoraFilter;
}

const SourceOraModal: React.FC<SourceOraModalProps> = ({ isOpen, onClose, sourceoraFilter }) => {
  const { mutate, isPending, error, data } = useSourceora();
  const { isDarkMode } = useTheme();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`sm:max-w-4xl ${
        isDarkMode ? 'bg-gray-900 text-white border-gray-700' : 'bg-white text-black border-gray-200'
      }`}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full flex items-center justify-center bg-brand-primary/20">
              <Search size={20} className="text-brand-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">Source Results</h2>
              {isPending ? (
                <div className="flex items-center gap-2 mt-1">
                  <Loader2 className="animate-spin" size={16} />
                  <p className="text-sm text-muted-foreground">Searching for sources...</p>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">{data?.data.search_summary}</p>
              )}
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 mt-6">
          {isPending ? (
            <div className="p-6 rounded-lg border text-center bg-muted/50">
              <Loader2 className="animate-spin mx-auto mb-3" size={32} />
              <p className="text-muted-foreground">Loading source results...</p>
            </div>
          ) : error ? (
            <div className="p-6 rounded-lg border text-center bg-destructive/10 border-destructive/20">
              <p className="text-destructive">Failed to load source results. Please try again.</p>
            </div>
          ) : data?.data.results.map((result, index) => (
            <div
              key={index}
              className="p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
            >
              <div className="flex justify-between items-start gap-4">
                <div className="flex-1">
                  <h3 className="font-semibold text-foreground mb-2">
                    {result.store_name}
                  </h3>
                  <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground">
                    <div className="space-y-1">
                      <p><span className="font-medium">Price:</span> £{result.price}</p>
                      <p><span className="font-medium">Shipping:</span> £{result.shipping_cost}</p>
                      <p><span className="font-medium">Total:</span> £{result.total_cost}</p>
                    </div>
                    <div className="space-y-1">
                      <p><span className="font-medium">Condition:</span> {result.condition}</p>
                      <p><span className="font-medium">Availability:</span> {result.availability}</p>
                    </div>
                  </div>
                </div>
                <Button
                  asChild
                  variant="outline"
                  size="sm"
                  className="shrink-0"
                >
                  <a
                    href={result.product_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2"
                  >
                    View <ExternalLink size={16} />
                  </a>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SourceOraModal;
